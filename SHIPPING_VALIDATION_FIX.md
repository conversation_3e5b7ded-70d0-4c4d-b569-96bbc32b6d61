# إصلاح مشكلة validation حقول سعر التوصيل العامة

## المشكلة
كانت حقول "سعر التوصيل للمكتب (عام)" و "سعر التوصيل للمنزل (عام)" في تبويبة إعدادات النموذج تتطلب دائماً قيمة أكبر من صفر، مما يعني أنها تظهر رسالة خطأ تقول "ضع من واحد فما فوق" حتى عند محاولة إدخال القيمة صفر أو ترك الحقل فارغاً.

## سبب المشكلة
في دالة `render_number_field` في ملف `includes/class-rid-cod-customizer.php`، كانت القيمة الافتراضية لـ `min` هي `1`:

```php
$min = $args['min'] ?? 1; // Default minimum value to 1 for positive integers
```

هذا يعني أن جميع الحقول الرقمية التي تستخدم هذه الدالة بدون تحديد `min` صراحة ستتطلب قيمة أكبر من أو تساوي 1.

## الحل المطبق
تم تعديل استدعاء `add_settings_field` لحقلي سعر التوصيل العامة لتمرير `min => 0` و `step => 'any'`:

### قبل الإصلاح:
```php
add_settings_field('rid_cod_general_shipping_home', __('سعر التوصيل للمنزل (عام)', 'rid-cod'), array($this, 'render_number_field'), 'rid_cod_settings', 'rid_cod_section_form_control', ['id' => 'rid_cod_general_shipping_home', 'default' => 0, 'desc' => __('سعر التوصيل للمنزل عند إخفاء حقل الولايات. يُستخدم عندما يكون نوع التوصيل مفعل أو كافتراضي.', 'rid-cod')]);
```

### بعد الإصلاح:
```php
add_settings_field('rid_cod_general_shipping_home', __('سعر التوصيل للمنزل (عام)', 'rid-cod'), array($this, 'render_number_field'), 'rid_cod_settings', 'rid_cod_section_form_control', ['id' => 'rid_cod_general_shipping_home', 'default' => 0, 'min' => 0, 'step' => 'any', 'desc' => __('سعر التوصيل للمنزل عند إخفاء حقل الولايات. يُستخدم عندما يكون نوع التوصيل مفعل أو كافتراضي.', 'rid-cod')]);
```

## التغييرات المطبقة

### الملف: `includes/class-rid-cod-customizer.php`
- **السطر 250**: إضافة `'min' => 0, 'step' => 'any'` لحقل `rid_cod_general_shipping_home`
- **السطر 251**: إضافة `'min' => 0, 'step' => 'any'` لحقل `rid_cod_general_shipping_desk`

## النتيجة
الآن يمكن للمستخدمين:
- ✅ إدخال القيمة صفر (0) في حقول سعر التوصيل العامة
- ✅ ترك الحقول فارغة
- ✅ إدخال قيم عشرية (مثل 5.50)
- ✅ إدخال أي قيمة موجبة

## التحقق من الإصلاح
تم إنشاء ملفات اختبار للتحقق من عمل الإصلاح:
- `test-shipping-validation.php`: اختبار منطق الدالة
- `test-settings-form.html`: محاكاة واجهة الإعدادات

## ملاحظات مهمة
- الإصلاح لا يؤثر على الحقول الأخرى التي تحتاج فعلاً إلى قيم موجبة (مثل حدود الطلبات)
- sanitize callback للحقلين يستخدم `floatval` مما يعني أنه يقبل القيمة صفر بشكل طبيعي
- الكود في JavaScript يتعامل مع القيمة صفر بشكل صحيح ويعرضها كـ "توصيل مجاني"

## التوافق مع الإصدارات السابقة
الإصلاح متوافق تماماً مع الإصدارات السابقة ولا يؤثر على:
- الإعدادات الموجودة
- منطق حساب أسعار التوصيل
- عرض الأسعار في النموذج
- أي وظائف أخرى في الإضافة

## تاريخ الإصلاح
- **التاريخ**: 2025-07-20
- **الإصدار**: تم تطبيقه على الكود الحالي
- **المطور**: Augment Agent
