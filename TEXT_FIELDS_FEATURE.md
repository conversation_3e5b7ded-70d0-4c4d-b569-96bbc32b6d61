# ميزة تحويل الولايات والبلديات إلى حقول نصية

## نظرة عامة
تم إضافة ميزة جديدة تسمح بتحويل حقول الولايات والبلديات من القوائم المنسدلة إلى حقول نصية قابلة للكتابة، مع استخدام أسعار التوصيل العامة بدلاً من الأسعار المحددة لكل ولاية.

## الهدف من الميزة
- **تبسيط النموذج**: إزالة تعقيد القوائم المنسدلة للمتاجر التي لا تحتاج إليها
- **توحيد أسعار التوصيل**: استخدام سعر ثابت للتوصيل بدلاً من أسعار مختلفة لكل ولاية
- **مرونة أكبر**: السماح للعملاء بكتابة أي ولاية أو بلدية حتى لو لم تكن في القائمة
- **سهولة الإدارة**: تقليل الحاجة لإدارة قوائم الولايات والبلديات

## كيفية التفعيل
1. انتقل إلى **لوحة التحكم** > **WooCommerce** > **إعدادات الدفع عند الاستلام**
2. اختر تبويبة **"إعدادات النموذج"**
3. فعّل خيار **"تحويل الولايات والبلديات إلى حقول نصية"**
4. تأكد من تحديد أسعار التوصيل العامة:
   - **سعر التوصيل للمنزل (عام)**
   - **سعر التوصيل للمكتب (عام)**
5. احفظ الإعدادات

## كيف تعمل الميزة

### الوضع العادي (القوائم المنسدلة)
- حقول الولايات والبلديات تظهر كقوائم منسدلة
- أسعار التوصيل تختلف حسب الولاية المختارة
- يتم استخدام أسعار التوصيل المحددة لكل ولاية

### الوضع النصي (الحقول النصية)
- حقول الولايات والبلديات تظهر كحقول نصية قابلة للكتابة
- أسعار التوصيل ثابتة للجميع (الأسعار العامة)
- العملاء يمكنهم كتابة أي ولاية أو بلدية

## التغييرات التقنية

### الملفات المُعدلة

#### 1. `includes/class-rid-cod-customizer.php`
- إضافة إعداد جديد: `rid_cod_force_text_fields`
- إضافة حقل في واجهة الإعدادات

#### 2. `includes/class-rid-cod-form.php`
- إضافة متغير `$force_text_fields`
- تعديل شرط عرض الحقول ليشمل الميزة الجديدة
- تمرير الإعداد إلى JavaScript

#### 3. `assets/js/rid-cod.js`
- إضافة دالة `convertStateSelectToTextInput()`
- إضافة منطق للتعامل مع الحقول النصية
- استخدام أسعار التوصيل العامة عند تفعيل الميزة

### الإعدادات الجديدة
```php
// الإعداد الجديد
'rid_cod_force_text_fields' => 'no' // القيمة الافتراضية

// الاستخدام في JavaScript
rid_cod_params.force_text_fields = true/false
```

## سلوك الميزة

### عند التفعيل
1. **تحويل الحقول**: جميع حقول الولايات والبلديات تصبح حقول نصية
2. **أسعار التوصيل**: يتم استخدام الأسعار العامة فقط
3. **التحقق من الصحة**: الحقول تبقى مطلوبة ولكن تقبل أي نص
4. **حفظ البيانات**: يتم حفظ النص المُدخل كما هو

### عند إلغاء التفعيل
1. **استعادة القوائم**: الحقول تعود إلى القوائم المنسدلة
2. **أسعار متغيرة**: يتم استخدام أسعار مختلفة حسب الولاية
3. **التحقق المحدود**: العملاء مقيدون بالخيارات المتاحة في القوائم

## التوافق

### مع الميزات الموجودة
- ✅ متوافق مع إخفاء/إظهار حقول الولايات والبلديات
- ✅ متوافق مع أنواع التوصيل (منزل/مكتب)
- ✅ متوافق مع جميع أشكال النموذج
- ✅ متوافق مع الدول المختلفة

### مع الإعدادات الأخرى
- إذا كان حقل الولايات مخفي (`show_states = false`)، الميزة لا تؤثر
- إذا كان حقل البلديات مخفي (`show_cities = false`)، يتم تحويل الولايات فقط
- الميزة تعمل مع جميع إعدادات المظهر والألوان

## حالات الاستخدام المناسبة

### مناسبة للمتاجر التي:
- لديها سعر توصيل موحد لجميع المناطق
- تريد تبسيط عملية الطلب
- تتعامل مع مناطق غير مدرجة في القوائم الافتراضية
- تفضل المرونة على الدقة في تحديد المواقع

### غير مناسبة للمتاجر التي:
- لديها أسعار توصيل مختلفة حسب المنطقة
- تحتاج إلى دقة في تحديد المواقع لأغراض لوجستية
- تعتمد على بيانات الولايات والبلديات في التقارير

## الاختبار
تم إنشاء ملف اختبار `test-text-fields-feature.html` لمحاكاة:
- تبديل بين الوضعين
- عرض الفرق في أسعار التوصيل
- اختبار وظائف التحويل

## ملاحظات مهمة
1. **البيانات المحفوظة**: عند التبديل بين الوضعين، البيانات المُدخلة سابقاً قد تحتاج إعادة إدخال
2. **التقارير**: في الوضع النصي، بيانات الولايات والبلديات ستكون نصوص حرة وليس رموز محددة
3. **التحقق**: لا يتم التحقق من صحة أسماء الولايات والبلديات في الوضع النصي
4. **الأداء**: الوضع النصي أسرع لأنه لا يحتاج تحميل قوائم البيانات

## تاريخ الإضافة
- **التاريخ**: 2025-07-20
- **الإصدار**: تم إضافتها للكود الحالي
- **المطور**: Augment Agent
