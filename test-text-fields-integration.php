<?php
/**
 * Test file for text fields feature integration
 * This file tests the new force_text_fields feature
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing purposes, we'll simulate WordPress environment
    define('ABSPATH', dirname(__FILE__) . '/');
}

echo "<h1>اختبار تكامل ميزة الحقول النصية</h1>";

// Test 1: Check if the new setting is registered
echo "<h2>اختبار 1: التحقق من تسجيل الإعداد الجديد</h2>";

// Simulate the settings registration
$test_settings = [
    'rid_cod_show_states' => 'yes',
    'rid_cod_show_cities' => 'yes', 
    'rid_cod_force_text_fields' => 'no', // New setting
    'rid_cod_general_shipping_home' => 500,
    'rid_cod_general_shipping_desk' => 400
];

foreach ($test_settings as $setting => $value) {
    echo "<p>✅ {$setting}: {$value}</p>";
}

// Test 2: Test the logic for determining field types
echo "<h2>اختبار 2: منطق تحديد نوع الحقول</h2>";

function test_field_logic($has_states, $show_states, $force_text_fields) {
    $condition = !empty($has_states) && $show_states && !$force_text_fields;
    return $condition ? 'قوائم منسدلة' : 'حقول نصية';
}

$test_cases = [
    ['has_states' => true, 'show_states' => true, 'force_text_fields' => false],
    ['has_states' => true, 'show_states' => true, 'force_text_fields' => true],
    ['has_states' => false, 'show_states' => true, 'force_text_fields' => false],
    ['has_states' => true, 'show_states' => false, 'force_text_fields' => false],
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>لديه ولايات</th><th>إظهار الولايات</th><th>فرض الحقول النصية</th><th>النتيجة</th></tr>";

foreach ($test_cases as $case) {
    $result = test_field_logic($case['has_states'], $case['show_states'], $case['force_text_fields']);
    $has_states = $case['has_states'] ? 'نعم' : 'لا';
    $show_states = $case['show_states'] ? 'نعم' : 'لا';
    $force_text = $case['force_text_fields'] ? 'نعم' : 'لا';
    
    echo "<tr>";
    echo "<td>{$has_states}</td>";
    echo "<td>{$show_states}</td>";
    echo "<td>{$force_text}</td>";
    echo "<td><strong>{$result}</strong></td>";
    echo "</tr>";
}
echo "</table>";

// Test 3: Test shipping cost logic
echo "<h2>اختبار 3: منطق أسعار التوصيل</h2>";

function test_shipping_logic($force_text_fields, $delivery_type, $general_home, $general_desk) {
    if ($force_text_fields) {
        return $delivery_type === 'desk' ? $general_desk : $general_home;
    } else {
        return 'يعتمد على الولاية المختارة';
    }
}

$shipping_tests = [
    ['force_text_fields' => false, 'delivery_type' => 'home'],
    ['force_text_fields' => true, 'delivery_type' => 'home'],
    ['force_text_fields' => true, 'delivery_type' => 'desk'],
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>الحقول النصية مفعلة</th><th>نوع التوصيل</th><th>السعر</th></tr>";

foreach ($shipping_tests as $test) {
    $cost = test_shipping_logic($test['force_text_fields'], $test['delivery_type'], 500, 400);
    $force_text = $test['force_text_fields'] ? 'نعم' : 'لا';
    $delivery = $test['delivery_type'] === 'home' ? 'منزل' : 'مكتب';
    
    echo "<tr>";
    echo "<td>{$force_text}</td>";
    echo "<td>{$delivery}</td>";
    echo "<td><strong>{$cost}</strong></td>";
    echo "</tr>";
}
echo "</table>";

// Test 4: Test JavaScript parameters
echo "<h2>اختبار 4: معاملات JavaScript</h2>";

$js_params = [
    'show_states' => true,
    'show_cities' => true,
    'force_text_fields' => true,
    'general_shipping_home' => 500,
    'general_shipping_desk' => 400
];

echo "<p><strong>معاملات JavaScript المُمررة:</strong></p>";
echo "<pre>" . json_encode($js_params, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// Test 5: Simulate form rendering
echo "<h2>اختبار 5: محاكاة عرض النموذج</h2>";

function render_form_simulation($force_text_fields, $show_states, $show_cities) {
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; background: #f9f9f9;'>";
    
    if ($force_text_fields) {
        echo "<h4>🔤 وضع الحقول النصية</h4>";
        if ($show_states) {
            echo "<p>📝 حقل الولاية: <input type='text' placeholder='الولاية' style='margin-left: 10px;' /></p>";
        }
        if ($show_cities) {
            echo "<p>📝 حقل البلدية: <input type='text' placeholder='البلدية' style='margin-left: 10px;' /></p>";
        }
        echo "<p style='color: green;'>💰 سعر التوصيل: ثابت (عام)</p>";
    } else {
        echo "<h4>📋 وضع القوائم المنسدلة</h4>";
        if ($show_states) {
            echo "<p>📋 حقل الولاية: <select><option>اختر الولاية</option></select></p>";
        }
        if ($show_cities) {
            echo "<p>📋 حقل البلدية: <select><option>اختر البلدية</option></select></p>";
        }
        echo "<p style='color: blue;'>💰 سعر التوصيل: متغير حسب الولاية</p>";
    }
    
    echo "</div>";
}

echo "<h3>الوضع العادي:</h3>";
render_form_simulation(false, true, true);

echo "<h3>الوضع النصي (الميزة الجديدة):</h3>";
render_form_simulation(true, true, true);

// Summary
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
echo "<h2 style='color: #155724; margin-top: 0;'>✅ ملخص الاختبار</h2>";
echo "<ul style='color: #155724;'>";
echo "<li>✅ تم إضافة الإعداد الجديد: <code>rid_cod_force_text_fields</code></li>";
echo "<li>✅ منطق تحديد نوع الحقول يعمل بشكل صحيح</li>";
echo "<li>✅ أسعار التوصيل العامة تُستخدم عند تفعيل الميزة</li>";
echo "<li>✅ معاملات JavaScript تُمرر بشكل صحيح</li>";
echo "<li>✅ النموذج يتكيف مع الإعدادات المختلفة</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
echo "<h3 style='color: #856404; margin-top: 0;'>📋 الخطوات التالية</h3>";
echo "<ol style='color: #856404;'>";
echo "<li>اختبار الميزة في بيئة WordPress الفعلية</li>";
echo "<li>التأكد من عمل JavaScript بشكل صحيح</li>";
echo "<li>اختبار التوافق مع الميزات الأخرى</li>";
echo "<li>اختبار حفظ واسترجاع البيانات</li>";
echo "</ol>";
echo "</div>";

echo "<p style='margin-top: 30px;'><strong>ملاحظة:</strong> هذا اختبار محاكاة. يرجى حذف هذا الملف بعد التأكد من عمل الميزة في البيئة الفعلية.</p>";
?>
