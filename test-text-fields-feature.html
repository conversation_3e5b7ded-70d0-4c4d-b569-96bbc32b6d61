<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ميزة تحويل الحقول إلى نصية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f1f1f1;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-table {
            background: white;
            border: 1px solid #ddd;
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        .form-table th,
        .form-table td {
            padding: 15px;
            border-bottom: 1px solid #ddd;
            text-align: right;
        }
        .form-table th {
            background: #f9f9f9;
            font-weight: bold;
            width: 200px;
        }
        .rid-cod-field-group {
            margin: 15px 0;
            position: relative;
        }
        .rid-cod-field-with-icon {
            position: relative;
        }
        .rid-input-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            background: #666;
            z-index: 2;
        }
        .rid-icon-state::before {
            content: "🏛️";
        }
        .rid-icon-city::before {
            content: "🏙️";
        }
        select, input[type="text"] {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            background: white;
        }
        select:focus, input[type="text"]:focus {
            border-color: #007cba;
            outline: none;
        }
        .feature-demo {
            background: #e7f3ff;
            border-left: 4px solid #0073aa;
            padding: 15px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .toggle-button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .toggle-button:hover {
            background: #005a87;
        }
        .shipping-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>اختبار ميزة تحويل الولايات والبلديات إلى حقول نصية</h1>
    
    <div class="feature-demo success">
        <h2>✅ الميزة الجديدة</h2>
        <p><strong>الوصف:</strong> تحويل حقول الولايات والبلديات إلى حقول نصية قابلة للكتابة بدلاً من القوائم المنسدلة</p>
        <p><strong>الفائدة:</strong> استخدام أسعار التوصيل العامة بدلاً من أسعار محددة لكل ولاية</p>
        <p><strong>الموقع:</strong> إعدادات التحكم في النموذج → "تحويل الولايات والبلديات إلى حقول نصية"</p>
    </div>

    <div class="test-container">
        <h2>محاكاة الإعدادات</h2>
        <table class="form-table">
            <tr>
                <th scope="row">تحويل الولايات والبلديات إلى حقول نصية</th>
                <td>
                    <input type="checkbox" id="force_text_fields" name="force_text_fields" value="yes" />
                    <label for="force_text_fields">تفعيل</label>
                    <p class="description">عند التفعيل، ستصبح حقول الولايات والبلديات قابلة للكتابة بدلاً من القوائم المنسدلة، وسيتم استخدام أسعار التوصيل العامة.</p>
                </td>
            </tr>
            <tr>
                <th scope="row">سعر التوصيل للمنزل (عام)</th>
                <td>
                    <input type="number" id="general_home" name="general_home" value="500" min="0" step="any" class="small-text" />
                    <span>دج</span>
                    <p class="description">سيتم استخدام هذا السعر عند تفعيل الميزة</p>
                </td>
            </tr>
            <tr>
                <th scope="row">سعر التوصيل للمكتب (عام)</th>
                <td>
                    <input type="number" id="general_desk" name="general_desk" value="400" min="0" step="any" class="small-text" />
                    <span>دج</span>
                    <p class="description">سيتم استخدام هذا السعر عند تفعيل الميزة</p>
                </td>
            </tr>
        </table>
    </div>

    <div class="test-container">
        <h2>محاكاة النموذج</h2>
        <button class="toggle-button" onclick="toggleMode()">تبديل الوضع</button>
        <div class="shipping-info" id="shipping-info">
            <strong>وضع القوائم المنسدلة:</strong> أسعار مختلفة حسب الولاية المختارة
        </div>
        
        <div id="form-demo">
            <div class="rid-cod-field-group rid-cod-field-with-icon">
                <span class="rid-input-icon rid-icon-state"></span>
                <select id="demo-state" name="state" required>
                    <option value="" disabled selected>الولاية</option>
                    <option value="01" data-home="600" data-desk="500">01 - أدرار</option>
                    <option value="16" data-home="400" data-desk="300">16 - الجزائر</option>
                    <option value="19" data-home="550" data-desk="450">19 - سطيف</option>
                </select>
            </div>

            <div class="rid-cod-field-group rid-cod-field-with-icon">
                <span class="rid-input-icon rid-icon-city"></span>
                <select id="demo-city" name="city" required disabled>
                    <option value="" disabled selected>البلدية</option>
                </select>
            </div>
        </div>
        
        <div class="shipping-info">
            <strong>سعر التوصيل الحالي:</strong>
            <span id="current-shipping">اختر الولاية أولاً</span>
        </div>
    </div>

    <div class="feature-demo info">
        <h3>كيف تعمل الميزة:</h3>
        <ol>
            <li><strong>الوضع العادي:</strong> قوائم منسدلة + أسعار مختلفة لكل ولاية</li>
            <li><strong>الوضع النصي:</strong> حقول نصية + سعر ثابت (عام) للجميع</li>
            <li><strong>الفائدة:</strong> تبسيط النموذج وتوحيد أسعار التوصيل</li>
            <li><strong>الاستخدام:</strong> مناسب للمتاجر التي لديها سعر توصيل موحد</li>
        </ol>
    </div>

    <script>
        let isTextMode = false;
        const generalShipping = { home: 500, desk: 400 };
        
        function toggleMode() {
            isTextMode = !isTextMode;
            const checkbox = document.getElementById('force_text_fields');
            checkbox.checked = isTextMode;
            
            if (isTextMode) {
                convertToTextFields();
            } else {
                convertToSelectFields();
            }
            
            updateShippingInfo();
        }
        
        function convertToTextFields() {
            const stateField = document.getElementById('demo-state');
            const cityField = document.getElementById('demo-city');
            
            // Convert state to text input
            const stateInput = document.createElement('input');
            stateInput.type = 'text';
            stateInput.id = 'demo-state';
            stateInput.name = 'state';
            stateInput.placeholder = 'الولاية';
            stateInput.required = true;
            stateInput.addEventListener('input', updateShippingInfo);
            
            stateField.parentNode.replaceChild(stateInput, stateField);
            
            // Convert city to text input
            const cityInput = document.createElement('input');
            cityInput.type = 'text';
            cityInput.id = 'demo-city';
            cityInput.name = 'city';
            cityInput.placeholder = 'البلدية';
            cityInput.required = true;
            cityInput.addEventListener('input', updateShippingInfo);
            
            cityField.parentNode.replaceChild(cityInput, cityField);
        }
        
        function convertToSelectFields() {
            const stateField = document.getElementById('demo-state');
            const cityField = document.getElementById('demo-city');
            
            // Convert state to select
            const stateSelect = document.createElement('select');
            stateSelect.id = 'demo-state';
            stateSelect.name = 'state';
            stateSelect.required = true;
            stateSelect.innerHTML = `
                <option value="" disabled selected>الولاية</option>
                <option value="01" data-home="600" data-desk="500">01 - أدرار</option>
                <option value="16" data-home="400" data-desk="300">16 - الجزائر</option>
                <option value="19" data-home="550" data-desk="450">19 - سطيف</option>
            `;
            stateSelect.addEventListener('change', updateShippingInfo);
            
            stateField.parentNode.replaceChild(stateSelect, stateField);
            
            // Convert city to select
            const citySelect = document.createElement('select');
            citySelect.id = 'demo-city';
            citySelect.name = 'city';
            citySelect.required = true;
            citySelect.disabled = true;
            citySelect.innerHTML = '<option value="" disabled selected>البلدية</option>';
            
            cityField.parentNode.replaceChild(citySelect, cityField);
        }
        
        function updateShippingInfo() {
            const shippingInfo = document.getElementById('shipping-info');
            const currentShipping = document.getElementById('current-shipping');
            
            if (isTextMode) {
                shippingInfo.innerHTML = '<strong>وضع الحقول النصية:</strong> أسعار التوصيل العامة (ثابتة)';
                currentShipping.textContent = `للمنزل: ${generalShipping.home} دج | للمكتب: ${generalShipping.desk} دج`;
            } else {
                shippingInfo.innerHTML = '<strong>وضع القوائم المنسدلة:</strong> أسعار مختلفة حسب الولاية المختارة';
                
                const stateSelect = document.getElementById('demo-state');
                const selectedOption = stateSelect.options[stateSelect.selectedIndex];
                
                if (selectedOption && selectedOption.value) {
                    const homePrice = selectedOption.getAttribute('data-home');
                    const deskPrice = selectedOption.getAttribute('data-desk');
                    currentShipping.textContent = `للمنزل: ${homePrice} دج | للمكتب: ${deskPrice} دج`;
                } else {
                    currentShipping.textContent = 'اختر الولاية أولاً';
                }
            }
        }
        
        // Initialize
        document.getElementById('demo-state').addEventListener('change', updateShippingInfo);
        document.getElementById('force_text_fields').addEventListener('change', function() {
            if (this.checked !== isTextMode) {
                toggleMode();
            }
        });
    </script>
</body>
</html>
