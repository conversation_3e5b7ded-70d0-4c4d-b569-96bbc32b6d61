<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار عرض ملخص الطلب مع الحقول النصية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f1f1f1;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-section {
            background: #f9f9f9;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .summary-section {
            background: #e7f3ff;
            border: 1px solid #0073aa;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .delivery-options {
            margin: 15px 0;
        }
        .delivery-option {
            margin: 10px 0;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .delivery-option:hover {
            border-color: #0073aa;
        }
        .delivery-option.selected {
            border-color: #0073aa;
            background: #e7f3ff;
        }
        .delivery-option input[type="radio"] {
            margin-left: 10px;
        }
        .shipping-display {
            font-size: 18px;
            font-weight: bold;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .shipping-paid {
            background: #fff3cd;
            color: #856404;
        }
        .shipping-free {
            background: #d4edda;
            color: #155724;
        }
        .total-display {
            font-size: 20px;
            font-weight: bold;
            padding: 15px;
            background: #0073aa;
            color: white;
            border-radius: 5px;
            text-align: center;
        }
        .settings-panel {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .toggle-button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .toggle-button:hover {
            background: #005a87;
        }
        .toggle-button.active {
            background: #28a745;
        }
        input[type="number"] {
            width: 80px;
            padding: 5px;
            margin: 0 5px;
        }
        .free-shipping {
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>اختبار عرض ملخص الطلب مع ميزة الحقول النصية</h1>
    
    <div class="test-container">
        <h2>محاكاة الإعدادات</h2>
        <div class="settings-panel">
            <h3>إعدادات الميزة</h3>
            <label>
                <input type="checkbox" id="force_text_fields" />
                تفعيل ميزة الحقول النصية
            </label>
            <br><br>
            
            <h4>أسعار التوصيل العامة:</h4>
            <label>للمنزل: <input type="number" id="general_home" value="500" min="0" /> دج</label>
            <label>للمكتب: <input type="number" id="general_desk" value="400" min="0" /> دج</label>
            <br><br>
            
            <label>نص التوصيل المجاني: <input type="text" id="free_shipping_text" value="توصيل مجاني" /></label>
        </div>
    </div>

    <div class="test-container">
        <h2>محاكاة النموذج</h2>
        
        <div class="form-section">
            <h3>حقول العنوان</h3>
            <div id="address-fields">
                <!-- سيتم تحديث هذا القسم بناءً على الإعدادات -->
            </div>
        </div>

        <div class="form-section">
            <h3>نوع التوصيل</h3>
            <div class="delivery-options">
                <div class="delivery-option" data-type="home">
                    <label>
                        <input type="radio" name="delivery_type" value="home" checked />
                        توصيل للمنزل
                    </label>
                </div>
                <div class="delivery-option" data-type="desk">
                    <label>
                        <input type="radio" name="delivery_type" value="desk" />
                        توصيل للمكتب
                    </label>
                </div>
            </div>
        </div>

        <div class="summary-section">
            <h3>ملخص الطلب</h3>
            <div>
                <strong>المنتج:</strong> مطحنة التوابل الكورية
                <br>
                <strong>الكمية:</strong> 1
                <br>
                <strong>سعر المنتج:</strong> 1500 دج
            </div>
            
            <div class="shipping-display" id="shipping-display">
                <strong>سعر التوصيل:</strong> <span id="shipping-price">500 دج</span>
            </div>
            
            <div class="total-display" id="total-display">
                الإجمالي: <span id="total-price">2000 دج</span>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>سيناريوهات الاختبار</h2>
        <button class="toggle-button" onclick="testScenario('normal')">الوضع العادي</button>
        <button class="toggle-button" onclick="testScenario('text_fields')">الحقول النصية</button>
        <button class="toggle-button" onclick="testScenario('free_shipping')">توصيل مجاني</button>
        <button class="toggle-button" onclick="testScenario('zero_desk')">مكتب مجاني</button>
    </div>

    <script>
        const basePrice = 1500;
        let currentSettings = {
            force_text_fields: false,
            general_home: 500,
            general_desk: 400,
            free_shipping_text: 'توصيل مجاني'
        };

        function updateSettings() {
            currentSettings.force_text_fields = document.getElementById('force_text_fields').checked;
            currentSettings.general_home = parseInt(document.getElementById('general_home').value) || 0;
            currentSettings.general_desk = parseInt(document.getElementById('general_desk').value) || 0;
            currentSettings.free_shipping_text = document.getElementById('free_shipping_text').value || 'توصيل مجاني';
            
            updateAddressFields();
            updateShippingDisplay();
        }

        function updateAddressFields() {
            const addressFields = document.getElementById('address-fields');
            
            if (currentSettings.force_text_fields) {
                addressFields.innerHTML = `
                    <p><strong>وضع الحقول النصية مفعل</strong></p>
                    <input type="text" placeholder="الولاية" style="width: 200px; padding: 8px; margin: 5px;" />
                    <input type="text" placeholder="البلدية" style="width: 200px; padding: 8px; margin: 5px;" />
                    <p style="color: green;">✅ يتم استخدام أسعار التوصيل العامة</p>
                `;
            } else {
                addressFields.innerHTML = `
                    <p><strong>وضع القوائم المنسدلة</strong></p>
                    <select style="width: 200px; padding: 8px; margin: 5px;">
                        <option>اختر الولاية</option>
                        <option>الجزائر</option>
                        <option>أدرار</option>
                    </select>
                    <select style="width: 200px; padding: 8px; margin: 5px;">
                        <option>اختر البلدية</option>
                    </select>
                    <p style="color: blue;">ℹ️ أسعار مختلفة حسب الولاية</p>
                `;
            }
        }

        function updateShippingDisplay() {
            const deliveryType = document.querySelector('input[name="delivery_type"]:checked').value;
            const shippingDisplay = document.getElementById('shipping-display');
            const shippingPrice = document.getElementById('shipping-price');
            const totalPrice = document.getElementById('total-price');
            
            let shippingCost = 0;
            
            if (currentSettings.force_text_fields) {
                // استخدام الأسعار العامة
                shippingCost = deliveryType === 'desk' ? currentSettings.general_desk : currentSettings.general_home;
            } else {
                // محاكاة أسعار مختلفة حسب الولاية
                shippingCost = deliveryType === 'desk' ? 300 : 600; // أسعار مختلفة للمثال
            }
            
            // عرض السعر أو "توصيل مجاني"
            if (shippingCost > 0) {
                shippingPrice.textContent = shippingCost + ' دج';
                shippingDisplay.className = 'shipping-display shipping-paid';
            } else {
                shippingPrice.innerHTML = '<span class="free-shipping">' + currentSettings.free_shipping_text + '</span>';
                shippingDisplay.className = 'shipping-display shipping-free';
            }
            
            // حساب الإجمالي
            const total = basePrice + shippingCost;
            totalPrice.textContent = total + ' دج';
        }

        function testScenario(scenario) {
            // إزالة التفعيل من جميع الأزرار
            document.querySelectorAll('.toggle-button').forEach(btn => btn.classList.remove('active'));
            
            switch(scenario) {
                case 'normal':
                    document.getElementById('force_text_fields').checked = false;
                    document.getElementById('general_home').value = 500;
                    document.getElementById('general_desk').value = 400;
                    event.target.classList.add('active');
                    break;
                    
                case 'text_fields':
                    document.getElementById('force_text_fields').checked = true;
                    document.getElementById('general_home').value = 500;
                    document.getElementById('general_desk').value = 400;
                    event.target.classList.add('active');
                    break;
                    
                case 'free_shipping':
                    document.getElementById('force_text_fields').checked = true;
                    document.getElementById('general_home').value = 0;
                    document.getElementById('general_desk').value = 0;
                    event.target.classList.add('active');
                    break;
                    
                case 'zero_desk':
                    document.getElementById('force_text_fields').checked = true;
                    document.getElementById('general_home').value = 500;
                    document.getElementById('general_desk').value = 0;
                    event.target.classList.add('active');
                    break;
            }
            
            updateSettings();
        }

        // ربط الأحداث
        document.getElementById('force_text_fields').addEventListener('change', updateSettings);
        document.getElementById('general_home').addEventListener('input', updateSettings);
        document.getElementById('general_desk').addEventListener('input', updateSettings);
        document.getElementById('free_shipping_text').addEventListener('input', updateSettings);

        // ربط أحداث نوع التوصيل
        document.querySelectorAll('input[name="delivery_type"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // تحديث المظهر
                document.querySelectorAll('.delivery-option').forEach(option => {
                    option.classList.remove('selected');
                });
                this.closest('.delivery-option').classList.add('selected');
                
                // تحديث الأسعار
                updateShippingDisplay();
            });
        });

        // تهيئة أولية
        updateSettings();
        document.querySelector('.delivery-option[data-type="home"]').classList.add('selected');
    </script>
</body>
</html>
