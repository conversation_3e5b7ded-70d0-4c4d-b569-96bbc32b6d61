<?php
/**
 * Test file for shipping validation fix
 * This file tests the general shipping cost fields to ensure they accept zero values
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing purposes, we'll simulate WordPress environment
    define('ABSPATH', dirname(__FILE__) . '/');
}

// Test function to simulate the render_number_field behavior
function test_render_number_field($args) {
    $option_value = $args['default'] ?? 0;
    $min = $args['min'] ?? 1; // This was the original problem
    $step = $args['step'] ?? 1;
    
    echo "<h4>Testing field: {$args['id']}</h4>";
    echo "<p>Default value: {$option_value}</p>";
    echo "<p>Min value: {$min}</p>";
    echo "<p>Step value: {$step}</p>";
    echo "<p>HTML: &lt;input type=\"number\" min=\"{$min}\" step=\"{$step}\" value=\"{$option_value}\" /&gt;</p>";
    
    if ($min > 0 && $option_value == 0) {
        echo "<p style='color: red;'>❌ Problem: Min value is {$min} but default is 0 - this will cause validation error!</p>";
    } else {
        echo "<p style='color: green;'>✅ OK: Field accepts zero values</p>";
    }
    echo "<hr>";
}

echo "<h1>اختبار إصلاح validation حقول سعر التوصيل</h1>";

echo "<h2>الحقول قبل الإصلاح (المشكلة):</h2>";

// Test old behavior (before fix)
test_render_number_field([
    'id' => 'rid_cod_general_shipping_home',
    'default' => 0
    // No min specified, so it defaults to 1 - this was the problem
]);

test_render_number_field([
    'id' => 'rid_cod_general_shipping_desk', 
    'default' => 0
    // No min specified, so it defaults to 1 - this was the problem
]);

echo "<h2>الحقول بعد الإصلاح (الحل):</h2>";

// Test new behavior (after fix)
test_render_number_field([
    'id' => 'rid_cod_general_shipping_home',
    'default' => 0,
    'min' => 0,
    'step' => 'any'
]);

test_render_number_field([
    'id' => 'rid_cod_general_shipping_desk',
    'default' => 0, 
    'min' => 0,
    'step' => 'any'
]);

echo "<h2>اختبار قيم مختلفة:</h2>";

$test_values = [0, '', '0', 0.0, '0.0', 5, '5.50'];

foreach ($test_values as $value) {
    $sanitized = floatval($value);
    echo "<p>القيمة: " . var_export($value, true) . " → بعد floatval(): {$sanitized}</p>";
}

echo "<h2>الخلاصة:</h2>";
echo "<div style='padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
echo "<h3 style='color: #155724;'>✅ تم إصلاح المشكلة بنجاح!</h3>";
echo "<p><strong>المشكلة كانت:</strong> دالة render_number_field تستخدم min='1' كقيمة افتراضية</p>";
echo "<p><strong>الحل:</strong> تمرير min='0' و step='any' لحقول سعر التوصيل العامة</p>";
echo "<p><strong>النتيجة:</strong> الآن يمكن للمستخدمين إدخال قيمة صفر أو ترك الحقل فارغاً</p>";
echo "</div>";

echo "<p style='margin-top: 20px;'><strong>ملاحظة:</strong> يرجى حذف هذا الملف بعد التأكد من عمل الإصلاح.</p>";
?>
