<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نموذج إعدادات سعر التوصيل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f1f1f1;
        }
        .form-table {
            background: white;
            border: 1px solid #ddd;
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        .form-table th,
        .form-table td {
            padding: 15px;
            border-bottom: 1px solid #ddd;
            text-align: right;
        }
        .form-table th {
            background: #f9f9f9;
            font-weight: bold;
            width: 200px;
        }
        .small-text {
            width: 80px;
        }
        .description {
            color: #666;
            font-style: italic;
            margin-top: 5px;
            display: block;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        input[type="number"]:invalid {
            border-color: #dc3545;
            background-color: #fff5f5;
        }
        input[type="number"]:valid {
            border-color: #28a745;
            background-color: #f8fff8;
        }
        .validation-message {
            margin-top: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>اختبار إصلاح validation حقول سعر التوصيل</h1>
    
    <div class="test-section error">
        <h2>❌ المشكلة السابقة (قبل الإصلاح)</h2>
        <p>الحقول كانت تتطلب قيمة أكبر من 1 بسبب <code>min="1"</code></p>
        
        <table class="form-table">
            <tr>
                <th scope="row">سعر التوصيل للمنزل (عام) - قبل الإصلاح</th>
                <td>
                    <input type="number" id="old_home" name="old_home" value="0" class="small-text" min="1" step="1" />
                    <span class="description">هذا الحقل سيظهر خطأ validation لأن القيمة 0 أقل من الحد الأدنى 1</span>
                    <div class="validation-message" id="old_home_msg"></div>
                </td>
            </tr>
            <tr>
                <th scope="row">سعر التوصيل للمكتب (عام) - قبل الإصلاح</th>
                <td>
                    <input type="number" id="old_desk" name="old_desk" value="0" class="small-text" min="1" step="1" />
                    <span class="description">هذا الحقل سيظهر خطأ validation لأن القيمة 0 أقل من الحد الأدنى 1</span>
                    <div class="validation-message" id="old_desk_msg"></div>
                </td>
            </tr>
        </table>
    </div>

    <div class="test-section success">
        <h2>✅ الحل الجديد (بعد الإصلاح)</h2>
        <p>الحقول الآن تقبل قيمة صفر أو أي قيمة موجبة بسبب <code>min="0"</code> و <code>step="any"</code></p>
        
        <table class="form-table">
            <tr>
                <th scope="row">سعر التوصيل للمنزل (عام)</th>
                <td>
                    <input type="number" id="new_home" name="new_home" value="0" class="small-text" min="0" step="any" />
                    <span class="description">سعر التوصيل للمنزل عند إخفاء حقل الولايات. يُستخدم عندما يكون نوع التوصيل مفعل أو كافتراضي.</span>
                    <div class="validation-message" id="new_home_msg"></div>
                </td>
            </tr>
            <tr>
                <th scope="row">سعر التوصيل للمكتب (عام)</th>
                <td>
                    <input type="number" id="new_desk" name="new_desk" value="0" class="small-text" min="0" step="any" />
                    <span class="description">سعر التوصيل للمكتب عند إخفاء حقل الولايات. يُستخدم عندما يكون نوع التوصيل غير مفعل أو عند اختيار التوصيل للمكتب.</span>
                    <div class="validation-message" id="new_desk_msg"></div>
                </td>
            </tr>
        </table>
    </div>

    <div class="test-section">
        <h2>اختبار قيم مختلفة</h2>
        <p>جرب إدخال قيم مختلفة في الحقول أدناه لترى الفرق:</p>
        
        <table class="form-table">
            <tr>
                <th scope="row">اختبار قيم مختلفة</th>
                <td>
                    <label>قيمة صفر: <input type="number" value="0" min="0" step="any" class="small-text" /></label><br><br>
                    <label>قيمة فارغة: <input type="number" value="" min="0" step="any" class="small-text" /></label><br><br>
                    <label>قيمة عشرية: <input type="number" value="5.50" min="0" step="any" class="small-text" /></label><br><br>
                    <label>قيمة صحيحة: <input type="number" value="10" min="0" step="any" class="small-text" /></label>
                </td>
            </tr>
        </table>
    </div>

    <script>
        // Check validation status for old fields
        function checkValidation() {
            const oldHome = document.getElementById('old_home');
            const oldDesk = document.getElementById('old_desk');
            const newHome = document.getElementById('new_home');
            const newDesk = document.getElementById('new_desk');
            
            // Check old fields
            if (!oldHome.checkValidity()) {
                document.getElementById('old_home_msg').innerHTML = '<span style="color: red;">❌ خطأ: القيمة أقل من الحد الأدنى المطلوب (1)</span>';
            } else {
                document.getElementById('old_home_msg').innerHTML = '<span style="color: green;">✅ صحيح</span>';
            }
            
            if (!oldDesk.checkValidity()) {
                document.getElementById('old_desk_msg').innerHTML = '<span style="color: red;">❌ خطأ: القيمة أقل من الحد الأدنى المطلوب (1)</span>';
            } else {
                document.getElementById('old_desk_msg').innerHTML = '<span style="color: green;">✅ صحيح</span>';
            }
            
            // Check new fields
            if (!newHome.checkValidity()) {
                document.getElementById('new_home_msg').innerHTML = '<span style="color: red;">❌ خطأ</span>';
            } else {
                document.getElementById('new_home_msg').innerHTML = '<span style="color: green;">✅ صحيح - يقبل القيمة صفر</span>';
            }
            
            if (!newDesk.checkValidity()) {
                document.getElementById('new_desk_msg').innerHTML = '<span style="color: red;">❌ خطأ</span>';
            } else {
                document.getElementById('new_desk_msg').innerHTML = '<span style="color: green;">✅ صحيح - يقبل القيمة صفر</span>';
            }
        }
        
        // Run validation check on page load
        document.addEventListener('DOMContentLoaded', checkValidation);
        
        // Run validation check when values change
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', checkValidation);
        });
    </script>
</body>
</html>
