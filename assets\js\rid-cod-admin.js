jQuery(document).ready(function($) {
    // Functionality for admin settings tabs

    // The PHP side handles setting the 'active' class based on URL param during page load.
    // The CSS (.tab-content { display: none; } .tab-content.active { display: block; })
    // should be sufficient to show the correct tab initially.
    // No explicit JS show/hide needed on load.

    // Handle tab clicks (using delegation for potentially dynamic elements)
    $('.nav-tab-wrapper').on('click', '.nav-tab', function(e) {
        // Prevent default link behavior if needed, but WP tabs usually rely on page reload with query param
        // e.preventDefault();

        // // Get the target tab content ID from the href (e.g., #tab-labels)
        // var targetTab = $(this).attr('href').split('tab=')[1];
        // var targetContent = '#tab-' + targetTab;

        // // Remove active class from all tabs and content
        // $('.nav-tab').removeClass('nav-tab-active');
        // $('.tab-content').hide();

        // // Add active class to the clicked tab and show its content
        // $(this).addClass('nav-tab-active');
        // $(targetContent).show();

        // // Optional: Update URL hash without reloading (if not using query params)
        // // window.location.hash = targetTab;
    });

    // Note: The current PHP implementation reloads the page with a '?tab=' parameter.
    // The JavaScript above mainly ensures the correct tab is shown on page load.
    // The click handling part is commented out because the page reload handles the active state.
    // --- Copy Apps Script Code Button ---
    console.log('RID COD Admin JS: Script loaded.'); // Debug log
    var copyButton = $('#rid-cod-copy-script-button');

    if (copyButton.length) {
        console.log('RID COD Admin JS: Copy button found.'); // Debug log
        copyButton.on('click', function() {
            console.log('RID COD Admin JS: Copy button clicked.'); // Debug log
            var codeBlock = document.getElementById('rid-cod-apps-script-code');
            if (!codeBlock) {
                console.error('RID COD Admin JS: Code block element not found!'); // Debug log
                alert('خطأ: لم يتم العثور على عنصر الكود.'); // Error: Code element not found.
                return;
            }
            var textToCopy = codeBlock.innerText || codeBlock.textContent; // Get text content

            // Use the modern Clipboard API if available (requires HTTPS or localhost)
            if (navigator.clipboard && window.isSecureContext) {
                console.log('RID COD Admin JS: Using Clipboard API.'); // Debug log
                navigator.clipboard.writeText(textToCopy).then(function() {
                    // Success feedback (optional)
                    alert('تم نسخ الكود!'); // Alert: Code copied!
                }, function(err) {
                    // Error feedback (optional)
                    console.error('RID COD: Failed to copy code using Clipboard API: ', err);
                    fallbackCopyTextToClipboard(textToCopy); // Try fallback
                });
            } else {
                // Fallback for older browsers or insecure contexts
                console.log('RID COD Admin JS: Using fallback copy method.'); // Debug log
                fallbackCopyTextToClipboard(textToCopy);
            }
        }); // End of click handler
    } else {
         console.log('RID COD Admin JS: Copy button not found.'); // Debug log
    }

    // Fallback function for copying text
    function fallbackCopyTextToClipboard(text) {
        var textArea = document.createElement("textarea");
        textArea.value = text;

        // Avoid scrolling to bottom
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            var successful = document.execCommand('copy');
            if (successful) {
                alert('تم نسخ الكود!'); // Alert: Code copied!
            } else {
                 alert('فشل نسخ الكود.'); // Alert: Failed to copy code.
                 console.error('RID COD: Fallback copy command failed');
            }
        } catch (err) {
             alert('فشل نسخ الكود.'); // Alert: Failed to copy code.
             console.error('RID COD: Error during fallback copy command: ', err);
        }

        document.body.removeChild(textArea);
    }
    // --- End Copy Button ---

    // --- Copy Shortcode Button ---
    var copyShortcodeButton = $('#rid-cod-copy-shortcode-button');
    if (copyShortcodeButton.length) {
        console.log('RID COD Admin JS: Shortcode copy button found.'); // Added log
        copyShortcodeButton.on('click', function() {
            console.log('RID COD Admin JS: Shortcode copy button clicked.'); // Added log
            var shortcodeInput = document.getElementById('rid-cod-shortcode-text');
            if (!shortcodeInput) {
                alert('خطأ: لم يتم العثور على حقل الكود المختصر.'); // Error: Shortcode input field not found.
                return;
            }
            var textToCopy = shortcodeInput.value;

            // Use the same copy logic (modern API or fallback)
            if (navigator.clipboard && window.isSecureContext) {
                console.log('RID COD Admin JS: Attempting shortcode copy via Clipboard API.'); // Added log
                navigator.clipboard.writeText(textToCopy).then(function() {
                    console.log('RID COD Admin JS: Shortcode copied via Clipboard API successfully.'); // Added log
                    alert('تم نسخ الكود المختصر!'); // Alert: Shortcode copied!
                }, function(err) {
                    console.error('RID COD: Failed to copy shortcode using Clipboard API: ', err);
                    fallbackCopyTextToClipboard(textToCopy); // Try fallback
                });
            } else {
                console.log('RID COD Admin JS: Attempting shortcode copy via fallback method.'); // Added log
                fallbackCopyTextToClipboard(textToCopy);
            }
        });
    }
    // --- End Copy Shortcode Button ---

    // Initialize WordPress Color Picker
    $('.rid-cod-color-picker').wpColorPicker();
 
    // --- Clear Shipping Cache Button ---
    var $clearCacheButton = $('#rid-cod-clear-shipping-cache-btn');
    var $cacheStatus = $('#rid-cod-cache-status');
    var $spinner = $clearCacheButton.siblings('.spinner');
 
    if ($clearCacheButton.length) {
        $clearCacheButton.on('click', function() {
            // Show spinner, disable button, clear status
            $spinner.addClass('is-active');
            $clearCacheButton.prop('disabled', true);
            $cacheStatus.hide().removeClass('notice-success notice-error').text('');
 
            // AJAX request
            $.ajax({
                url: ridCodAdminData.ajaxUrl, // Localized URL
                type: 'POST',
                data: {
                    action: 'rid_cod_clear_shipping_cache', // PHP action hook
                    nonce: ridCodAdminData.clearCacheNonce // Localized nonce
                },
                success: function(response) {
                    if (response.success) {
                        $cacheStatus.text(response.data).addClass('notice-success').show();
                    } else {
                        // Display error message from server if available, otherwise generic
                        var errorMessage = response.data ? ridCodAdminData.errorText + ' ' + response.data : ridCodAdminData.errorText + ' Unknown error.';
                        $cacheStatus.text(errorMessage).addClass('notice-error').show();
                        console.error('RID COD Cache Clear Error:', response);
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    $cacheStatus.text(ridCodAdminData.errorText + ' ' + textStatus).addClass('notice-error').show();
                    console.error('RID COD Cache Clear AJAX Error:', textStatus, errorThrown);
                },
                complete: function() {
                    // Hide spinner, re-enable button
                    $spinner.removeClass('is-active');
                    $clearCacheButton.prop('disabled', false);
                }
            });
        });
    }
    // --- End Clear Shipping Cache Button ---

    // --- State Management Functions ---

    // Modal elements
    var $modal = $('#rid-cod-state-modal');
    var $modalTitle = $('#rid-cod-modal-title');
    var $stateForm = $('#rid-cod-state-form');
    var $stateCodeInput = $('#rid-cod-state-code');
    var $stateNameInput = $('#rid-cod-state-name');
    var $originalStateCodeInput = $('#rid-cod-original-state-code');
    var $modalActionInput = $('#rid-cod-modal-action');
    var $saveStateBtn = $('#rid-cod-save-state-btn');
    var $cancelStateBtn = $('#rid-cod-cancel-state-btn');
    var $modalClose = $('.rid-cod-modal-close');
    var $modalStatus = $('#rid-cod-modal-status');
    var $stateManagementStatus = $('#rid-cod-state-management-status');

    // Add State Button
    $('#rid-cod-add-state-btn').on('click', function() {
        openStateModal('add');
    });

    // Edit State Button (delegated event)
    $(document).on('click', '.rid-cod-edit-state-btn', function() {
        var stateCode = $(this).data('state-code');
        var stateName = $(this).data('state-name');
        openStateModal('edit', stateCode, stateName);
    });

    // Delete State Button (delegated event)
    $(document).on('click', '.rid-cod-delete-state-btn', function() {
        var stateCode = $(this).data('state-code');
        var stateName = $(this).data('state-name');

        if (confirm(ridCodAdminData.confirmDeleteState)) {
            deleteState(stateCode);
        }
    });

    // Restore Default States Button
    $('#rid-cod-restore-default-states-btn').on('click', function() {
        if (confirm(ridCodAdminData.confirmRestoreStates)) {
            restoreDefaultStates();
        }
    });

    // Modal close events
    $modalClose.on('click', closeStateModal);
    $cancelStateBtn.on('click', closeStateModal);

    // Close modal when clicking outside
    $modal.on('click', function(e) {
        if (e.target === this) {
            closeStateModal();
        }
    });

    // Save State Button
    $saveStateBtn.on('click', function() {
        var action = $modalActionInput.val();
        if (action === 'add') {
            addState();
        } else if (action === 'edit') {
            updateState();
        }
    });

    // Form submission
    $stateForm.on('submit', function(e) {
        e.preventDefault();
        $saveStateBtn.click();
    });

    function openStateModal(action, stateCode, stateName) {
        if (action === 'add') {
            $modalTitle.text(ridCodAdminData.addStateTitle);
            $stateCodeInput.val('');
            $stateNameInput.val('');
            $originalStateCodeInput.val('');
            $modalActionInput.val('add');
        } else if (action === 'edit') {
            $modalTitle.text(ridCodAdminData.editStateTitle);
            $stateCodeInput.val(stateCode);
            $stateNameInput.val(stateName);
            $originalStateCodeInput.val(stateCode);
            $modalActionInput.val('edit');
        }

        $modalStatus.hide();
        $modal.show();
        $stateCodeInput.focus();
    }

    function closeStateModal() {
        $modal.hide();
        $stateForm[0].reset();
        $modalStatus.hide();
    }

    function addState() {
        var stateCode = $stateCodeInput.val().trim();
        var stateName = $stateNameInput.val().trim();

        if (!stateCode || !stateName) {
            showModalStatus(ridCodAdminData.errorText + ' جميع الحقول مطلوبة.', 'error');
            return;
        }

        $saveStateBtn.prop('disabled', true).text(ridCodAdminData.processingText);

        $.ajax({
            url: ridCodAdminData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'rid_cod_add_state',
                nonce: ridCodAdminData.stateManagementNonce,
                country_code: ridCodAdminData.currentCountry,
                state_code: stateCode,
                state_name: stateName
            },
            success: function(response) {
                if (response.success) {
                    showModalStatus(response.data.message, 'success');
                    setTimeout(function() {
                        closeStateModal();
                        addStateRowToTable(response.data.state_code, response.data.state_name);
                    }, 1500);
                } else {
                    showModalStatus(ridCodAdminData.errorText + ' ' + response.data, 'error');
                }
            },
            error: function() {
                showModalStatus(ridCodAdminData.errorText + ' حدث خطأ في الاتصال.', 'error');
            },
            complete: function() {
                $saveStateBtn.prop('disabled', false).text('حفظ');
            }
        });
    }

    function updateState() {
        var originalStateCode = $originalStateCodeInput.val();
        var stateCode = $stateCodeInput.val().trim();
        var stateName = $stateNameInput.val().trim();

        if (!stateCode || !stateName) {
            showModalStatus(ridCodAdminData.errorText + ' جميع الحقول مطلوبة.', 'error');
            return;
        }

        $saveStateBtn.prop('disabled', true).text(ridCodAdminData.processingText);

        $.ajax({
            url: ridCodAdminData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'rid_cod_update_state',
                nonce: ridCodAdminData.stateManagementNonce,
                country_code: ridCodAdminData.currentCountry,
                original_state_code: originalStateCode,
                state_code: stateCode,
                state_name: stateName
            },
            success: function(response) {
                if (response.success) {
                    showModalStatus(response.data.message, 'success');
                    setTimeout(function() {
                        closeStateModal();
                        location.reload(); // Reload to show updated state
                    }, 1500);
                } else {
                    showModalStatus(ridCodAdminData.errorText + ' ' + response.data, 'error');
                }
            },
            error: function() {
                showModalStatus(ridCodAdminData.errorText + ' حدث خطأ في الاتصال.', 'error');
            },
            complete: function() {
                $saveStateBtn.prop('disabled', false).text('حفظ');
            }
        });
    }

    function deleteState(stateCode) {
        showStateManagementStatus(ridCodAdminData.processingText, 'processing');

        $.ajax({
            url: ridCodAdminData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'rid_cod_delete_state',
                nonce: ridCodAdminData.stateManagementNonce,
                country_code: ridCodAdminData.currentCountry,
                state_code: stateCode
            },
            success: function(response) {
                if (response.success) {
                    showStateManagementStatus(response.data.message, 'success');
                    setTimeout(function() {
                        location.reload(); // Reload to remove deleted state
                    }, 1500);
                } else {
                    showStateManagementStatus(ridCodAdminData.errorText + ' ' + response.data, 'error');
                }
            },
            error: function() {
                showStateManagementStatus(ridCodAdminData.errorText + ' حدث خطأ في الاتصال.', 'error');
            }
        });
    }

    function restoreDefaultStates() {
        showStateManagementStatus(ridCodAdminData.processingText, 'processing');

        $.ajax({
            url: ridCodAdminData.ajaxUrl,
            type: 'POST',
            data: {
                action: 'rid_cod_restore_default_states',
                nonce: ridCodAdminData.stateManagementNonce,
                country_code: ridCodAdminData.currentCountry
            },
            success: function(response) {
                if (response.success) {
                    showStateManagementStatus(response.data.message, 'success');
                    setTimeout(function() {
                        location.reload(); // Reload to show default states
                    }, 1500);
                } else {
                    showStateManagementStatus(ridCodAdminData.errorText + ' ' + response.data, 'error');
                }
            },
            error: function() {
                showStateManagementStatus(ridCodAdminData.errorText + ' حدث خطأ في الاتصال.', 'error');
            }
        });
    }

    function showModalStatus(message, type) {
        $modalStatus.removeClass('notice-success notice-error notice-warning')
                   .addClass('notice-' + (type === 'success' ? 'success' : 'error'))
                   .text(message)
                   .show();
    }

    function showStateManagementStatus(message, type) {
        $stateManagementStatus.removeClass('notice-success notice-error notice-warning')
                              .addClass('notice-' + (type === 'success' ? 'success' : type === 'processing' ? 'warning' : 'error'))
                              .text(message)
                              .show();
    }

    function addStateRowToTable(stateCode, stateName) {
        var $tbody = $('.form-table tbody');
        if ($tbody.length === 0) {
            // If no tbody exists, reload the page
            location.reload();
            return;
        }

        var currentCountry = ridCodAdminData.currentCountry;
        var newRow = `
            <tr valign="top">
                <th scope="row">${stateName} (${stateCode})</th>
                <td>
                    <input type="number" step="any" min="0"
                           name="rid_cod_shipping_costs_${currentCountry}[${stateCode}][desk]"
                           value=""
                           class="small-text rid-cod-state-desk-cost"
                           data-state="${stateCode}"
                           placeholder="" />
                    <span class="description">(افتراضي: 0)</span>
                </td>
                <td>
                    <input type="number" step="any" min="0"
                           name="rid_cod_shipping_costs_${currentCountry}[${stateCode}][home]"
                           value=""
                           class="small-text rid-cod-state-home-cost"
                           data-state="${stateCode}"
                           placeholder="" />
                    <span class="description">(افتراضي: 0)</span>
                </td>
                <td>
                    <button type="button" class="button button-small rid-cod-edit-state-btn"
                            data-state-code="${stateCode}"
                            data-state-name="${stateName}"
                            title="تعديل الولاية">
                        <span class="dashicons dashicons-edit" style="vertical-align: middle;"></span>
                    </button>
                    <button type="button" class="button button-small rid-cod-delete-state-btn"
                            data-state-code="${stateCode}"
                            data-state-name="${stateName}"
                            title="حذف الولاية"
                            style="margin-left: 5px;">
                        <span class="dashicons dashicons-trash" style="vertical-align: middle; color: #dc3232;"></span>
                    </button>
                </td>
            </tr>
        `;

        // Insert the new row in the correct position (sorted by state code)
        var inserted = false;
        $tbody.find('tr').each(function() {
            var existingStateCode = $(this).find('.rid-cod-state-desk-cost').data('state');
            if (existingStateCode && parseInt(stateCode) < parseInt(existingStateCode)) {
                $(this).before(newRow);
                inserted = true;
                return false; // Break the loop
            }
        });

        if (!inserted) {
            $tbody.append(newRow);
        }
    }

    // --- End State Management Functions ---

}); // End of jQuery(document).ready()